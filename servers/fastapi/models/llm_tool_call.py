from typing import Literal, Optional
from pydantic import BaseModel


class <PERSON><PERSON><PERSON>Call(BaseModel):
    pass


class Open<PERSON><PERSON>oolCallFunction(BaseModel):
    name: str
    arguments: str


class OpenAIToolCall(LLMToolCall):
    id: str
    type: Literal["function"] = "function"
    function: OpenAIToolCallFunction


class GoogleToolCall(LLMToolCall):
    id: Optional[str] = None
    name: str
    arguments: Optional[dict] = None


class AnthropicToolCall(LLMToolCall):
    type: Literal["tool_use"] = "tool_use"
    id: str
    name: str
    input: object
